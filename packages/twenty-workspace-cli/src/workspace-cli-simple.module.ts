import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

// Import commands
import { DemoListCommand } from './commands/demo-list.command';

// Import utilities
import { CommandLogger } from './utils/command-logger';

// Simplified workspace entity for demo
import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'workspace', schema: 'core' })
export class SimpleWorkspace {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  displayName?: string;

  @Column({ unique: true })
  subdomain!: string;

  @Column({ nullable: true })
  customDomain?: string;

  @Column({ 
    type: 'enum',
    enum: ['PENDING_CREATION', 'ONGOING_CREATION', 'ACTIVE', 'INACTIVE', 'DELETED'],
    default: 'INACTIVE'
  })
  activationStatus!: string;

  @Column({ default: true })
  allowImpersonation!: boolean;

  @Column({ default: true })
  isPublicInviteLinkEnabled!: boolean;

  @Column({ default: true })
  isGoogleAuthEnabled!: boolean;

  @Column({ default: true })
  isPasswordAuthEnabled!: boolean;

  @Column({ default: true })
  isMicrosoftAuthEnabled!: boolean;

  @Column({ default: false })
  isCustomDomainEnabled!: boolean;

  @Column({ default: 1 })
  metadataVersion!: number;

  @Column({ nullable: true })
  version?: string;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamptz' })
  deletedAt?: Date;
}

// Simplified services for demo
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class SimpleWorkspaceService {
  constructor(
    @InjectRepository(SimpleWorkspace, 'core')
    private readonly workspaceRepository: Repository<SimpleWorkspace>,
  ) {}

  async findById(id: string): Promise<SimpleWorkspace | null> {
    return await this.workspaceRepository.findOne({ where: { id } });
  }

  async findAll(): Promise<SimpleWorkspace[]> {
    return await this.workspaceRepository.find();
  }

  async isSubdomainAvailable(subdomain: string): Promise<boolean> {
    const existing = await this.workspaceRepository.findOne({ where: { subdomain } });
    return !existing;
  }

  async updateWorkspaceById(params: { payload: Partial<SimpleWorkspace> & { id: string } }): Promise<SimpleWorkspace> {
    await this.workspaceRepository.update(params.payload.id, params.payload);
    const workspace = await this.findById(params.payload.id);
    if (!workspace) throw new Error('Workspace not found after update');
    return workspace;
  }

  async deleteWorkspace(id: string, softDelete = true): Promise<SimpleWorkspace> {
    const workspace = await this.findById(id);
    if (!workspace) throw new Error('Workspace not found');

    if (softDelete) {
      await this.workspaceRepository.softDelete(id);
    } else {
      await this.workspaceRepository.delete(id);
    }

    return workspace;
  }
}

@Injectable()
export class SimpleTwentyConfigService {
  get(key: string): any {
    return process.env[key];
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      expandVariables: true,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({
        type: 'postgres',
        url: process.env.PG_DATABASE_URL || 'postgresql://postgres:password@localhost:5432/twenty',
        ssl: process.env.PG_SSL_ALLOW_SELF_SIGNED === 'true' 
          ? { rejectUnauthorized: false }
          : undefined,
        logging: process.env.DEBUG_MODE === 'true',
        schema: 'core',
        entities: [SimpleWorkspace],
        synchronize: false,
      }),
    }),
    TypeOrmModule.forFeature([SimpleWorkspace], 'core'),
  ],
  controllers: [],
  providers: [
    // Simplified services
    SimpleTwentyConfigService,
    SimpleWorkspaceService,
    
    // CLI utilities
    CommandLogger,
    
    // Demo commands
    DemoListCommand,
  ],
  exports: [
    SimpleWorkspaceService,
    CommandLogger,
  ],
})
export class WorkspaceCliSimpleModule {}